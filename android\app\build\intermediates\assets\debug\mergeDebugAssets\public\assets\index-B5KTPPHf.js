import{r as n}from"./index--8kKyf6A.js";import"./ui-Bu5Z5a0w.js";import"./vendor-COrNHRvO.js";import"./audio-5UOY9KLB.js";import"./supabase-DLIhrfaA.js";import"./audioManager-oMWoQbcF.js";import"./game-BIyZFdIB.js";var e;(function(t){t.notDetermined="notDetermined",t.restricted="restricted",t.denied="denied",t.authorized="authorized"})(e||(e={}));const r=n("AppTrackingTransparency");function i(t){return()=>t().then(o=>o.value)}class h{constructor(){this.getStatus=i(r.getStatus),this.requestPermission=i(r.requestPermission)}}export{h as AppTrackingTransparency,e as AppTrackingTransparencyStatus};

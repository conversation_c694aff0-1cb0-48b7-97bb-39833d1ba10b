@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400&family=Merriweather:ital,wght@0,300;0,400;0,700;1,400&family=Poppins:wght@300;400;500;600;700&display=swap");
@import "./styles/mobile.css";
@import "./styles/animations.css";
@import "./styles/player-icons.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Animazioni personalizzate */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 3s ease-in-out infinite;
}

/* Animazioni CSS rimosse - non più utilizzate */

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    --radius: 1rem;
  }

  .dark {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply text-maraffa-text font-sans overflow-x-hidden;
    font-family: "Poppins", "Inter", sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: radial-gradient(
      ellipse at center,
      #fefce8 0%,
      #fef3c7 25%,
      #fed7aa 60%,
      #fed7d7 85%,
      #fecaca 100%
    );
    background-attachment: fixed;
    position: relative;
  }

  /* Sfondo specifico per la pagina di gioco - verde scuro tipo tappeto */
  body.game-active {
    background: radial-gradient(
      ellipse at center,
      #1a4d3a 0%,
      #0f3d2c 25%,
      #0a2e21 60%,
      #052016 85%,
      #03140e 100%
    ) !important;
  }
  body::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("/images/logos/logo_bastoni_compressed.png");
    background-repeat: space;
    opacity: 0.12;
    filter: sepia(80%) saturate(120%) hue-rotate(15deg) brightness(0.7)
      contrast(1.1);
    pointer-events: none;
    z-index: -1;
    transform: rotate(8deg) scale(1.1);
    transform-origin: center center;
    /* Logo più piccolo con spaziatura per creare un pattern alternato */
    background-size: 60px 60px;
    background-position: 0 0;
    /* Usa background-repeat: space per distribuire i loghi e creare un effetto alternato */
    /* Animazioni combinate più visibili per i loghi */
    animation: logoFloat 12s ease-in-out infinite,
      logoShimmer 8s ease-in-out infinite 2s,
      logoWave 10s ease-in-out infinite 1s;
  }

  /* Pattern di sfondo verde per la modalità gioco */
  body.game-active::before {
    filter: sepia(100%) saturate(200%) hue-rotate(90deg) brightness(0.6)
      contrast(1.2) !important;
    opacity: 0.08 !important;
  }

  h1,
  h2,
  h3,
  h4,
  h5 {
    font-family: "Playfair Display", serif;
    @apply text-maraffa-primary;
  }

  h1 {
    @apply text-3xl md:text-4xl lg:text-5xl font-bold leading-tight;
  }

  h2 {
    @apply text-2xl md:text-3xl font-semibold leading-tight;
  }

  h3 {
    @apply text-xl md:text-2xl font-medium leading-snug;
  }
}

.glass {
  @apply backdrop-blur-md bg-white/70 border border-white/20 shadow-lg;
}

.rustic-glass {
  @apply backdrop-blur-sm bg-romagna-cream/80 border border-romagna-gold/30 shadow-md;
}

.wood-panel {
  background-color: #7d5a44;
  background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23603f28' fill-opacity='0.3' fill-rule='evenodd'%3E%3Cpath d='M0 38.59l2.83-2.83 1.41 1.41L1.41 40H0v-1.41zM0 1.4l2.83 2.83 1.41-1.41L1.41 0H0v1.41zM38.59 40l-2.83-2.83 1.41-1.41L40 38.59V40h-1.41zM40 1.41l-2.83 2.83-1.41-1.41L38.59 0H40v1.41zM20 18.6l2.83-2.83 1.41 1.41L21.41 20l2.83 2.83-1.41 1.41L20 21.41l-2.83 2.83-1.41-1.41L18.59 20l-2.83-2.83 1.41-1.41L20 18.59z'/%3E%3C/g%3E%3C/svg%3E");
  animation: backgroundPatternShift 10s ease-in-out infinite;
}

.parchment {
  background-color: #f7f3e9;
  background-image: url("data:image/svg+xml,%3Csvg width='52' height='26' viewBox='0 0 52 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23d4a35a' fill-opacity='0.09'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  animation: backgroundPatternShift 14s ease-in-out infinite reverse;
}

.romagna-pattern {
  background-color: #f5ecd7;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='20' viewBox='0 0 100 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21.184 20c.357-.13.72-.264 1.088-.402l1.768-.661C33.64 15.347 39.647 14 50 14c10.271 0 15.362 1.222 24.629 4.928.955.383 1.869.74 2.75 1.072h6.225c-2.51-.73-5.139-1.691-8.233-2.928C65.888 13.278 60.562 12 50 12c-10.626 0-16.855 1.397-26.66 5.063l-1.767.662c-2.475.923-4.66 1.674-6.724 2.275h6.335zm0-20C13.258 2.892 8.077 4 0 4V2c5.744 0 9.951-.574 14.85-2h6.334zM77.38 0C85.239 2.966 90.502 4 100 4V2c-6.842 0-11.386-.542-16.396-2h-6.225zM0 14c8.44 0 13.718-1.21 22.272-4.402l1.768-.661C33.64 5.347 39.647 4 50 4c10.271 0 15.362 1.222 24.629 4.928C84.112 12.722 89.438 14 100 14v-2c-10.271 0-15.362-1.222-24.629-4.928C65.888 3.278 60.562 2 50 2 39.374 2 33.145 3.397 23.34 7.063l-1.767.662C13.223 10.84 8.163 12 0 12v2z' fill='%238b2c1c' fill-opacity='0.13' fill-rule='evenodd'/%3E%3C/svg%3E");
  animation: backgroundPatternShift 8s ease-in-out infinite;
}

.glass-dark {
  @apply backdrop-blur-md bg-black/20 border border-white/10 shadow-lg;
}

.card-shadow {
  box-shadow: 0 6px 15px -3px rgba(124, 90, 68, 0.25),
    0 4px 6px -2px rgba(124, 90, 68, 0.15);
}

.romagna-shadow {
  box-shadow: 0 6px 12px -2px rgba(139, 44, 28, 0.15),
    0 3px 6px -3px rgba(139, 44, 28, 0.1);
}

.card-hover {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 25px -5px rgba(124, 90, 68, 0.2),
    0 10px 10px -5px rgba(124, 90, 68, 0.15);
}

.card-active {
  transform: translateY(-10px);
  box-shadow: 0 20px 25px -5px rgba(124, 90, 68, 0.25),
    0 10px 10px -5px rgba(124, 90, 68, 0.2);
}

.gradient-text {
  @apply text-transparent bg-clip-text bg-gradient-to-r from-maraffa-primary to-romagna-gold;
}

.rustic-border {
  @apply border-2 border-romagna-gold/30 rounded-md;
}

.rustic-button {
  @apply bg-gradient-to-b from-romagna-gold to-romagna-gold/80 text-white py-2 px-4 rounded-md shadow-md hover:from-romagna-gold/90 hover:to-romagna-gold/70 transition-all duration-300;
}

.rustic-card {
  @apply bg-romagna-cream rounded-md border border-romagna-gold/20 shadow-md hover:shadow-lg transition-all duration-300;
}

/* Animations for page transitions */
.page-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 400ms, transform 400ms;
}

.page-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 300ms, transform 300ms;
}

.loading-shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.6) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Nuovi stili per il tema rustico romagnolo */
@keyframes flicker {
  0%,
  100% {
    opacity: 1;
  }
  25% {
    opacity: 0.95;
  }
  50% {
    opacity: 0.97;
  }
  75% {
    opacity: 0.93;
  }
}

@keyframes grain {
  0%,
  100% {
    background-position: 0% 0%;
  }
  20% {
    background-position: 20% 0%;
  }
  40% {
    background-position: 40% 20%;
  }
  60% {
    background-position: 60% 40%;
  }
  80% {
    background-position: 80% 60%;
  }
}

.tavern-wood {
  background-color: #5c4129;
  background-image: url("data:image/svg+xml,%3Csvg width='30' height='30' viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 0C6.716 0 0 6.716 0 15c8.284 0 15-6.716 15-15zM0 15c0 8.284 6.716 15 15 15 0-8.284-6.716-15-15-15zm30 0c0-8.284-6.716-15-15-15 0 8.284 6.716 15 15 15zm0 0c0 8.284-6.716 15-15 15 0-8.284 6.716-15 15-15z' fill='%237d5a44' fill-opacity='0.4' fill-rule='evenodd'/%3E%3C/svg%3E");
  animation: backgroundPatternShift 18s ease-in-out infinite;
}

.rustic-paper {
  background-color: #f5ecd7;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z' fill='%23af8d55' fill-opacity='0.08' fill-rule='evenodd'/%3E%3C/svg%3E");
  animation: backgroundPatternShift 16s ease-in-out infinite reverse;
}

.romagna-scratches {
  background-color: #f7f3e9;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%238b2c1c' fill-opacity='0.05' fill-rule='evenodd'%3E%3Cpath d='M0 38.59l2.83-2.83 1.41 1.41L1.41 40H0v-1.41zM0 1.4l2.83 2.83 1.41-1.41L1.41 0H0v1.41zM38.59 40l-2.83-2.83 1.41-1.41L40 38.59V40h-1.41zM40 1.41l-2.83 2.83-1.41-1.41L38.59 0H40v1.41zM20 18.6l2.83-2.83 1.41 1.41L21.41 20l2.83 2.83-1.41 1.41L20 21.41l-2.83 2.83-1.41-1.41L18.59 20l-2.83-2.83 1.41-1.41L20 18.59z'/%3E%3C/g%3E%3C/svg%3E");
  animation: backgroundPatternShift 9s ease-in-out infinite;
}

.romagna-osteria {
  background-color: #7d5a44;
  background-image: url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 0h2v20H9V0zm25.134.84l1.732 1-10 17.32-1.732-1 10-17.32zm-20 20l1.732 1-10 17.32-1.732-1 10-17.32zM58.16 4.134l1 1.732-17.32 10-1-1.732 17.32-10zm-40 40l1 1.732-17.32 10-1-1.732 17.32-10zM80 9v2H60V9h20zM20 69v2H0v-2h20zm79.32-55l-1 1.732-17.32-10L82 4l17.32 10zm-80 80l-1 1.732-17.32-10L2 84l17.32 10zm96.546-75.84l-1.732 1-10-17.32 1.732-1 10 17.32zm-100 100l-1.732 1-10-17.32 1.732-1 10 17.32zM38.16 24.134l1 1.732-17.32 10-1-1.732 17.32-10zM60 29v2H40v-2h20zm19.32 5l-1 1.732-17.32-10L62 24l17.32 10zm16.546 4.16l-1.732 1-10-17.32 1.732-1 10 17.32zM111 40h-2V20h2v20zm3.134.84l1.732 1-10 17.32-1.732-1 10-17.32zM40 49v2H20v-2h20zm19.32 5l-1 1.732-17.32-10L42 44l17.32 10zm-40 40l-1 1.732-17.32-10L2 84l17.32 10zm79.32-55l-1 1.732-17.32-10L82 4l17.32 10zm-80 80l-1 1.732-17.32-10L2 84l17.32 10zM38.16 84.134l1 1.732-17.32 10-1-1.732 17.32-10zm40-40l1 1.732-17.32 10-1-1.732 17.32-10zm39.32 5l-1 1.732-17.32-10L100 49l17.32 10zm-40 40l-1 1.732-17.32-10L60 89l17.32 10zm40-40l-1 1.732-17.32-10L80 69l17.32 10zm-20 20l-1 1.732-17.32-10L60 89l17.32 10zm59.32-65l-1 1.732-17.32-10L100 29l17.32 10zm-58.32 78l-1 1.732-17.32-10L80 89l17.32 10zm38.32-38l-1 1.732-17.32-10L60 89l17.32 10zm40-40l-1 1.732-17.32-10L98 64l17.32 10zm-39.32 38l-1 1.732-17.32-10L80 89l17.32 10zM30 0h2v20h-2V0zm0 40h2v20h-2V40zM10 40h2v20h-2V40zm30 0h2v20h-2V40zm40 0h2v20h-2V40zm30 0h2v20h-2V40zm-30-40h2v20h-2V0zm30 0h2v20h-2V0z' fill='%235c4129' fill-opacity='0.4' fill-rule='evenodd'/%3E%3C/svg%3E");
  animation: backgroundPatternShift 22s ease-in-out infinite reverse;
}

.romagna-board {
  background-color: #7d5a44;
  background-image: linear-gradient(45deg, #5c4129 25%, transparent 25%),
    linear-gradient(-45deg, #5c4129 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #5c4129 75%),
    linear-gradient(-45deg, transparent 75%, #5c4129 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

/* Decorazioni rustiche */
.decorative-border {
  position: relative;
}

.decorative-border::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1px solid rgba(175, 141, 85, 0.3);
  box-sizing: border-box;
  pointer-events: none;
}

.decorative-border::after {
  content: "";
  position: absolute;
  top: 5px;
  left: 5px;
  right: 5px;
  bottom: 5px;
  border: 1px solid rgba(175, 141, 85, 0.2);
  box-sizing: border-box;
  pointer-events: none;
}

.rustico-button {
  position: relative;
  display: inline-block;
  padding: 0.5rem 1.25rem;
  background: linear-gradient(to bottom, #8b2c1c, #702318);
  color: #f5ecd7;
  font-family: "Playfair Display", serif;
  font-weight: 600;
  border-radius: 0.25rem;
  border: 1px solid #8b2c1c;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
  cursor: pointer;
  overflow: hidden;
}

.rustico-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 40%;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.15),
    rgba(255, 255, 255, 0)
  );
  border-radius: 0.25rem 0.25rem 0 0;
}

.rustico-button:hover {
  background: linear-gradient(to bottom, #9a3121, #7e281a);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.rustico-button:active {
  background: linear-gradient(to bottom, #7e281a, #702318);
  transform: translateY(1px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Utility per line-clamp */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
}

/* Animazioni per i toast migliorati */
@keyframes toastSlideIn {
  0% {
    transform: translateX(-50%) translateY(-60px) scale(0.8) rotate(3deg);
    opacity: 0;
  }
  60% {
    transform: translateX(-50%) translateY(-10px) scale(1.05) rotate(-1deg);
    opacity: 0.9;
  }
  100% {
    transform: translateX(-50%) translateY(0px) scale(1) rotate(0deg);
    opacity: 1;
  }
}

@keyframes toastSlideOut {
  0% {
    transform: translateX(-50%) translateY(0px) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(-60px) scale(0.8);
    opacity: 0;
  }
}

@keyframes toastBounceIcon {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

/* Utility per shadow personalizzate */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25),
    0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Supporto per safe areas iOS - Solo per iOS */
@supports (-webkit-touch-callout: none) {
  .ios-safe-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }

  .ios-safe-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }

  .ios-safe-left {
    padding-left: max(1rem, env(safe-area-inset-left));
  }

  .ios-safe-right {
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}

/* Classi utility per il layout */
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

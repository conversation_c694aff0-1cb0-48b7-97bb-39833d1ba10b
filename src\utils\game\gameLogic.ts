import {
  Card,
  Suit,
  createDeck,
  shuffleDeck,
  dealCards,
  getWinningCard,
  Rank,
} from "./cardUtils";
// Memory utils removed for simplification
import { audioManager } from "@/utils/audio/AudioManager";
import { hasMaraffaInHand, isMaraffa } from "./maraffaUtils";
import { applyDebugMaraffaForcing } from "../debug/forceMaraffa";
import {
  StrategicAnnouncement,
  evaluateStrategicAnnouncement,
  updateAIMemoryWithAnnouncement,
} from "./strategicAnnouncements";

/**
 * 🎯 Trova automaticamente la Maraffa nella mano di un giocatore
 * Ritorna il seme se il giocatore ha A+2+3 dello stesso seme
 */
export const findAutomaticMaraffa = (hand: Card[]): Suit | null => {
  const suits = [Suit.Coins, Suit.Cups, Suit.Swords, Suit.Clubs];

  for (const suit of suits) {
    if (hasMaraffaInHand(hand, suit)) {
      return suit;
    }
  }

  return null;
};

/**
 * 🎯 Verifica se il giocatore corrente deve selezionare automaticamente la briscola per Maraffa
 * CONDIZIONE IMPORTANTE: Solo durante la fase di selezione briscola!
 */
export const checkForAutomaticMaraffa = (
  state: GameState
): {
  hasMaraffa: boolean;
  maraffaSuit: Suit | null;
  playerIndex: number;
} => {
  // 🚨 CORREZIONE: La Maraffa può essere rilevata SOLO durante la fase di selezione briscola
  if (state.gamePhase !== "selectTrump") {
    return {
      hasMaraffa: false,
      maraffaSuit: null,
      playerIndex: -1,
    };
  }

  const currentPlayer = state.players[state.currentPlayer];
  const maraffaSuit = findAutomaticMaraffa(currentPlayer.hand);

  return {
    hasMaraffa: maraffaSuit !== null,
    maraffaSuit,
    playerIndex: state.currentPlayer,
  };
};

/**
 * 🎯 NUOVO: Funzione per applicare automaticamente la selezione della briscola quando c'è una Maraffa
 * Questa funzione viene chiamata dall'interfaccia quando viene rilevata una Maraffa automatica
 */
export const applyAutomaticMaraffa = (state: GameState): GameState => {
  const automaticMaraffa = checkForAutomaticMaraffa(state);

  if (!automaticMaraffa.hasMaraffa || !automaticMaraffa.maraffaSuit) {
    return state;
  }

  // Applica automaticamente la selezione della briscola
  return selectTrump(state, automaticMaraffa.maraffaSuit);
};

export type Player = {
  id: number;
  name: string;
  hand: Card[];
  team: 0 | 1; // Team 0 or Team 1
  position: "north" | "east" | "south" | "west"; // Position at table
};

export type Team = {
  id: 0 | 1;
  players: [Player, Player];
  score: number;
  tricksWon: Card[];
  currentRoundPoints: number; // Punti interi accumulati nel round corrente
  currentRoundFigures: number; // Figure accumulate nel round corrente (0-2)
};

export type GameState = {
  deck: Card[];
  players: Player[];
  teams: [Team, Team];
  currentPlayer: number;
  trumpSuit: Suit | null;
  leadSuit: Suit | null;
  currentTrick: Card[];
  trickNumber: number;
  gamePhase:
    | "setup"
    | "selectTrump"
    | "play"
    | "scoring"
    | "gameOver"
    | "roundOver";
  playerWithFourOfCoins: number | null;
  roundScore: [number, number]; // Score for current round [team0, team1]
  gameScore: [number, number]; // Total game score [team0, team1]
  lastTrickWinner: number | null;
  announcedAction: string | null; // busso, striscio, volo
  message: string;
  leadPlayer: number; // Adding the leadPlayer property
  currentRoundScoreHistory: { team0: number[]; team1: number[] }; // Track score history within a round
  victoryPoints: number; // Configurable victory points (21, 31, or 41)
  lastTrumpSelector: number | null; // Chi ha scelto la briscola nel round precedente
  maraffeMade: [number, number]; // Maraffe realizzate per squadra [team0, team1]
  maxScoreDifference: number; // Massima differenza di punteggio raggiunta
  automaticMaraffa: {
    // 🎯 NUOVO: Traccia Maraffa automatica
    hasMaraffa: boolean;
    maraffaSuit: Suit | null;
    playerIndex: number;
  } | null;
};

export const initializeGameState = (victoryPoints: number = 31): GameState => {
  // Create and shuffle the deck
  const deck = shuffleDeck(createDeck());

  // Create players
  const players: Player[] = [
    { id: 0, name: "Tu", hand: [], team: 0, position: "south" }, // Sud (giocatore umano)
    { id: 1, name: "Player 2", hand: [], team: 1, position: "east" }, // Est
    { id: 2, name: "Player 3", hand: [], team: 0, position: "north" }, // Nord
    { id: 3, name: "Player 4", hand: [], team: 1, position: "west" }, // Ovest
  ];

  // Deal 10 cards to each player
  let hands = dealCards(deck, 4, 10);

  // Applica debug per forzare la maraffa se abilitato
  hands = applyDebugMaraffaForcing(hands);

  players.forEach((player, index) => {
    player.hand = hands[index];
  });

  // Create teams
  const teams: [Team, Team] = [
    {
      id: 0,
      players: [players[0], players[2]], // Tu (sud) e Player 3 (nord) - Squadra 1
      score: 0,
      tricksWon: [],
      currentRoundPoints: 0,
      currentRoundFigures: 0,
    },
    {
      id: 1,
      players: [players[1], players[3]], // Player 2 (est) e Player 4 (ovest) - Squadra 2
      score: 0,
      tricksWon: [],
      currentRoundPoints: 0,
      currentRoundFigures: 0,
    },
  ];

  // Find player with four of coins
  const playerWithFourOfCoins = players.findIndex((player) =>
    player.hand.some(
      (card) => card.suit === Suit.Coins && card.rank === Rank.Four
    )
  );

  const initialPlayer =
    playerWithFourOfCoins !== -1 ? playerWithFourOfCoins : 0;

  // 🎯 CORREZIONE: Verifica se il giocatore iniziale ha una maraffa automatica
  const tempState = {
    players,
    currentPlayer: initialPlayer,
  } as GameState;
  const automaticMaraffa = checkForAutomaticMaraffa(tempState);

  const initialState = {
    deck,
    players,
    teams,
    currentPlayer: initialPlayer,
    trumpSuit: null,
    leadSuit: null,
    currentTrick: [],
    trickNumber: 1,
    gamePhase: "selectTrump" as const,
    playerWithFourOfCoins,
    roundScore: [0, 0] as [number, number],
    gameScore: [0, 0] as [number, number],
    lastTrickWinner: null,
    announcedAction: null,
    message: "Il giocatore con il 4 di Denari deve scegliere la briscola",
    leadPlayer: initialPlayer,
    currentRoundScoreHistory: { team0: [], team1: [] },
    victoryPoints,
    lastTrumpSelector: null,
    maraffeMade: [0, 0] as [number, number],
    maxScoreDifference: 0,
    automaticMaraffa: automaticMaraffa.hasMaraffa ? automaticMaraffa : null,
  };

  // 🎯 CORREZIONE: Solo la CPU applica automaticamente la maraffa, il giocatore umano sceglie
  if (
    automaticMaraffa.hasMaraffa &&
    automaticMaraffa.maraffaSuit &&
    initialPlayer !== 0
  ) {
    // Solo se NON è il giocatore umano (id 0), applica automaticamente
    return selectTrump(initialState, automaticMaraffa.maraffaSuit);
  }

  return initialState;
};

export const selectTrump = (state: GameState, suit: Suit): GameState => {
  if (state.gamePhase !== "selectTrump") {
    return state;
  }

  const currentPlayer = state.players[state.currentPlayer];
  const newTeams = [...state.teams] as [Team, Team];

  // 🎯 CORREZIONE MARAFFA: Non assegnare punti immediatamente, ma solo marcare che ha la Maraffa
  // I punti verranno assegnati quando l'Asso viene effettivamente giocato come prima carta
  const maraffaBonus = 0; // I punti saranno assegnati in playCard, non qui
  const newMaraffeMade: [number, number] = [...state.maraffeMade];
  let hasValidMaraffa = false;

  // Controlla se il giocatore che sceglie la briscola ha la Maraffa
  if (isMaraffa(currentPlayer.hand, suit)) {
    hasValidMaraffa = true;
    console.log(
      `� MARAFFA RILEVATA! Team ${currentPlayer.team} ha A+2+3 di ${suit}. I punti verranno assegnati quando l'Asso sarà giocato come prima carta.`
    );
    // NON assegniamo i punti qui! Li assegneremo in playCard quando l'Asso viene giocato
  } else {
    console.log(
      `❌ NESSUNA MARAFFA: Team ${currentPlayer.team} non ha A+2+3 di ${suit}`
    );
  }
  return {
    ...state,
    trumpSuit: suit,
    gamePhase: "play",
    teams: newTeams,
    maraffeMade: newMaraffeMade,
    automaticMaraffa: null, // 🎯 Reset dopo aver selezionato la briscola
    message: `La briscola è ${
      suit === Suit.Coins
        ? "Denari"
        : suit === Suit.Cups
        ? "Coppe"
        : suit === Suit.Swords
        ? "Spade"
        : "Bastoni"
    }. ${state.players[state.currentPlayer].name} inizia.${
      hasValidMaraffa
        ? " Maraffa rilevata! I punti saranno assegnati quando l'Asso verrà giocato."
        : ""
    }`,
    leadPlayer: state.currentPlayer, // Set leadPlayer to currentPlayer when selecting trump
    lastTrumpSelector: state.currentPlayer, // Memorizza chi ha scelto la briscola
  };
};

export const announceAction = (
  state: GameState,
  action: "busso" | "striscio" | "volo"
): GameState => {
  // 🗣️ DICHIARAZIONI STRATEGICHE: Solo il primo giocatore del turno può dichiarare
  if (state.gamePhase !== "play" || state.currentTrick.length !== 0) {
    return state;
  }

  const currentPlayer = state.players[state.currentPlayer];

  // 🎯 Validazione delle dichiarazioni strategiche
  // Le regole sono meno rigide rispetto al vecchio sistema per permettere strategia
  console.log(
    `[STRATEGIC ANNOUNCEMENT] ${
      currentPlayer.name
    } dichiara: ${action.toUpperCase()}`
  );

  return {
    ...state,
    announcedAction: action,
    message: `${currentPlayer.name} dichiara: "${action.toUpperCase()}"`,
  };
};

// Sistema di calcolo punti corretto: contatori separati per assi e figure
export const calculateScore = (
  cards: Card[]
): {
  acePoints: number; // Punti da assi (1 punto per asso)
  figureCount: number; // Numero di figure trovate (0-4 per presa)
  totalPoints: number; // Solo per debug/display, non usato nei calcoli
} => {
  // Contiamo esattamente quante carte di ogni tipo ci sono
  const aces = cards.filter((card) => card.rank === Rank.Ace).length;
  const figures = cards.filter((card) =>
    [Rank.Three, Rank.Two, Rank.King, Rank.Horse, Rank.Jack].includes(card.rank)
  ).length;

  // Sistema preciso:
  // - Assi: 1 punto immediato ciascuno
  // - Figure: contate, ogni 3 figure = 1 punto
  const acePoints = aces;
  const figureCount = figures;

  // DEBUG: Log del calcolo dettagliato
  if (acePoints > 0 || figureCount > 0) {
    console.log(
      `    🧮 Calcolo: ${aces} assi = ${acePoints} punti, ${figures} figure`
    );
  }

  return {
    acePoints,
    figureCount,
    totalPoints: acePoints + Math.floor(figureCount / 3), // Solo per display
  };
};

// Sistema di calcolo preciso con contatori separati
const updateRunningScore = (
  state: GameState,
  trickWinner: number
): GameState => {
  const winnerTeam = state.players[trickWinner].team;
  const newTeams = [...state.teams] as [Team, Team];

  // PROTEZIONE: assicurati che il trick sia completo (4 carte)
  if (state.currentTrick.length !== 4) {
    console.warn(
      `⚠️ ERRORE: Tentativo di calcolare punteggio con trick incompleto (${state.currentTrick.length} carte)`
    );
    return state;
  }

  // Calcola i punti delle carte vinte nella presa attuale
  const scoreResult = calculateScore(state.currentTrick);

  // DEBUG: Log dei punti per ogni presa
  console.log(`🎯 PRESA ${state.trickNumber}:`);
  console.log(
    `   Carte giocate:`,
    state.currentTrick.map((c) => `${c.rank} di ${c.suit}`)
  );
  console.log(`   Punti da assi: ${scoreResult.acePoints}`);
  console.log(`   Figure trovate: ${scoreResult.figureCount}`);
  console.log(`   Team vincitore: ${winnerTeam}`);

  // Aggiorniamo lo storico dei punteggi per questo round
  const newScoreHistory = {
    team0: [...state.currentRoundScoreHistory.team0],
    team1: [...state.currentRoundScoreHistory.team1],
  };

  // Sistema preciso: aggiungi punti da assi immediatamente
  // e accumula figure fino a raggiungere gruppi di 3
  if (winnerTeam === 0) {
    const prevPoints = newTeams[0].currentRoundPoints;
    const prevFigures = newTeams[0].currentRoundFigures;

    // Aggiungi punti immediati da assi
    newTeams[0].currentRoundPoints += scoreResult.acePoints;

    // Accumula figure e converte ogni 3 in 1 punto
    const totalFigures = prevFigures + scoreResult.figureCount;
    const newFigurePoints = Math.floor(totalFigures / 3);
    newTeams[0].currentRoundPoints += newFigurePoints;
    newTeams[0].currentRoundFigures = totalFigures % 3;

    console.log(
      `   Team 0: ${prevPoints} + ${scoreResult.acePoints} (assi) + ${newFigurePoints} (da ${totalFigures} figure) = ${newTeams[0].currentRoundPoints}`
    );
    console.log(
      `   Team 0: Figure rimanenti: ${newTeams[0].currentRoundFigures}`
    );

    // Registra nello storico i punti totali assegnati in questa presa
    newScoreHistory.team0.push(scoreResult.acePoints + newFigurePoints);
  } else {
    const prevPoints = newTeams[1].currentRoundPoints;
    const prevFigures = newTeams[1].currentRoundFigures;

    // Aggiungi punti immediati da assi
    newTeams[1].currentRoundPoints += scoreResult.acePoints;

    // Accumula figure e converte ogni 3 in 1 punto
    const totalFigures = prevFigures + scoreResult.figureCount;
    const newFigurePoints = Math.floor(totalFigures / 3);
    newTeams[1].currentRoundPoints += newFigurePoints;
    newTeams[1].currentRoundFigures = totalFigures % 3;

    console.log(
      `   Team 1: ${prevPoints} + ${scoreResult.acePoints} (assi) + ${newFigurePoints} (da ${totalFigures} figure) = ${newTeams[1].currentRoundPoints}`
    );
    console.log(
      `   Team 1: Figure rimanenti: ${newTeams[1].currentRoundFigures}`
    );

    // Registra nello storico i punti totali assegnati in questa presa
    newScoreHistory.team1.push(scoreResult.acePoints + newFigurePoints);
  }

  // Aggiungi le carte al team vincitore per il conteggio finale
  newTeams[winnerTeam].tricksWon.push(...state.currentTrick);

  // Controlla se è l'ultima presa e assegna il bonus
  if (state.trickNumber === 10) {
    // Bonus ultima presa (bàga)
    const prevPoints = newTeams[winnerTeam].currentRoundPoints;
    newTeams[winnerTeam].currentRoundPoints += 1;

    console.log(
      `🏆 ULTIMA PRESA - Team ${winnerTeam}: ${prevPoints} + 1 (bonus) = ${newTeams[winnerTeam].currentRoundPoints}`
    );

    // Aggiungi il bonus all'ultima presa allo storico
    if (winnerTeam === 0) {
      newScoreHistory.team0.push(1); // Bonus ultima presa
    } else {
      newScoreHistory.team1.push(1); // Bonus ultima presa
    }

    console.log(
      `📊 FINE MANO - Totali: Team 0 = ${newTeams[0].currentRoundPoints}, Team 1 = ${newTeams[1].currentRoundPoints}`
    );
    console.log(
      `📊 SOMMA TOTALE: ${
        newTeams[0].currentRoundPoints + newTeams[1].currentRoundPoints
      } punti`
    );
    console.log(
      `📊 Figure rimaste: Team 0 = ${newTeams[0].currentRoundFigures}, Team 1 = ${newTeams[1].currentRoundFigures}`
    );
  }

  return {
    ...state,
    teams: newTeams,
    currentRoundScoreHistory: newScoreHistory,
    // Rimuoviamo figureTokens dal GameState perché ora è parte di Team
  };
};

export const playCard = (state: GameState, cardId: string): GameState => {
  if (state.gamePhase !== "play") {
    return state;
  }

  const currentPlayer = state.players[state.currentPlayer];
  const cardIndex = currentPlayer.hand.findIndex((card) => card.id === cardId);

  if (cardIndex === -1) {
    return state;
  }

  const card = currentPlayer.hand[cardIndex];

  // 🎯 REGOLA MARAFFA: Se il giocatore ha la Maraffa e apre il primo turno,
  // DEVE giocare l'asso della briscola come prima carta
  if (
    state.trumpSuit &&
    state.trickNumber === 1 && // Solo primo turno della mano
    state.currentTrick.length === 0 && // Prima carta del turno
    isMaraffa(currentPlayer.hand, state.trumpSuit) &&
    state.lastTrumpSelector === state.currentPlayer && // Chi ha scelto la briscola ha la Maraffa
    (card.suit !== state.trumpSuit || card.rank !== "A") // Non sta giocando l'asso di briscola
  ) {
    return {
      ...state,
      message:
        "🎯 MARAFFA! Devi giocare l'Asso della briscola come prima carta.",
    };
  }

  // Check if this is the first card in the trick
  if (state.currentTrick.length === 0) {
    // This player is leading, so any card is valid
    const newHand = [...currentPlayer.hand];
    newHand.splice(cardIndex, 1);

    const newPlayers = [...state.players];
    newPlayers[state.currentPlayer] = {
      ...currentPlayer,
      hand: newHand,
    };

    // 🎯 NUOVO SISTEMA MARAFFA: Assegna i 3 punti bonus SOLO quando l'Asso viene giocato come prima carta
    let maraffaBonus = 0;
    const maraffeMade: [number, number] = [...state.maraffeMade];
    const newTeams = [...state.teams] as [Team, Team];

    // 🚨 CONDIZIONI RIGOROSE PER MARAFFA BONUS:
    // 1. È il primo turno del round (trickNumber === 1)
    // 2. È la prima carta del turno (currentTrick.length === 0)
    // 3. La carta giocata è l'Asso di briscola
    // 4. Il giocatore che gioca è quello che ha scelto la briscola (lastTrumpSelector)
    // 5. Il giocatore ha effettivamente la Maraffa completa (A+2+3 del seme di briscola)
    if (
      state.trumpSuit &&
      state.trickNumber === 1 &&
      state.currentTrick.length === 0 &&
      card.suit === state.trumpSuit &&
      card.rank === "A" &&
      state.lastTrumpSelector === state.currentPlayer &&
      isMaraffa(currentPlayer.hand, state.trumpSuit)
    ) {
      // Verifica ulteriore: il giocatore deve avere ANCORA tutte e tre le carte
      // (incluso l'Asso che sta giocando)
      const hasAce = currentPlayer.hand.some(
        (c) => c.suit === state.trumpSuit && c.rank === "A"
      );
      const hasTwo = currentPlayer.hand.some(
        (c) => c.suit === state.trumpSuit && c.rank === "2"
      );
      const hasThree = currentPlayer.hand.some(
        (c) => c.suit === state.trumpSuit && c.rank === "3"
      );

      if (hasAce && hasTwo && hasThree) {
        maraffaBonus = 3;
        maraffeMade[currentPlayer.team] += 1;
        // 🚨 CORREZIONE CRITICA: I punti Maraffa devono essere aggiunti al currentRoundPoints,
        // non solo al punteggio generale, per essere conteggiati nel punteggio finale del round
        newTeams[currentPlayer.team].currentRoundPoints += maraffaBonus;

        console.log(
          `🎯🎯🎯 MARAFFA COMPLETATA! ${currentPlayer.name} (Team ${
            currentPlayer.team
          }) ha giocato l'Asso di briscola come prima carta. +${maraffaBonus} punti bonus! Round points aggiornati: ${
            newTeams[currentPlayer.team].currentRoundPoints
          }`
        );
      } else {
        console.log(
          `❌ MARAFFA FALSA: ${currentPlayer.name} non ha tutte le carte della Maraffa!`
        );
      }
    }

    // Calcola nuovi punteggi
    const newRoundScore: [number, number] = [
      state.roundScore[0] + (currentPlayer.team === 0 ? maraffaBonus : 0),
      state.roundScore[1] + (currentPlayer.team === 1 ? maraffaBonus : 0),
    ];

    // Aggiorna la massima differenza di punteggio
    const currentDifference = Math.abs(newRoundScore[0] - newRoundScore[1]);
    const maxScoreDifference = Math.max(
      state.maxScoreDifference,
      currentDifference
    );

    return {
      ...state,
      players: newPlayers,
      teams: newTeams, // Usa i nuovi teams con eventuali punti Maraffa
      currentTrick: [card],
      leadSuit: card.suit,
      leadPlayer: state.currentPlayer, // Set leadPlayer when starting a new trick
      currentPlayer: (state.currentPlayer + 1) % 4,
      roundScore: newRoundScore,
      maraffeMade,
      maxScoreDifference,
      announcedAction: null,
      message:
        maraffaBonus > 0
          ? `${currentPlayer.name} ha giocato ${card.displayName} e ottenuto il bonus Maraffa (+3 punti)!`
          : `${currentPlayer.name} ha giocato ${card.displayName}`,
    };
  }

  // Not the first card in the trick - check if it follows suit
  if (state.leadSuit && card.suit !== state.leadSuit) {
    // Check if the player has any cards of the lead suit
    if (currentPlayer.hand.some((c) => c.suit === state.leadSuit)) {
      return {
        ...state,
        message: `Devi giocare una carta di ${
          state.leadSuit === Suit.Coins
            ? "Denari"
            : state.leadSuit === Suit.Cups
            ? "Coppe"
            : state.leadSuit === Suit.Swords
            ? "Spade"
            : "Bastoni"
        } se ne hai una.`,
      };
    }
  }

  // Card is valid, play it
  const newHand = [...currentPlayer.hand];
  newHand.splice(cardIndex, 1);

  const newPlayers = [...state.players];
  newPlayers[state.currentPlayer] = {
    ...currentPlayer,
    hand: newHand,
  };

  const newTrick = [...state.currentTrick, card];

  // Check if the trick is complete (all 4 players have played)
  if (newTrick.length === 4) {
    // Determine the winner of the trick
    const winningCard = getWinningCard(
      newTrick,
      state.leadSuit!,
      state.trumpSuit!
    );
    const winningCardIndex = newTrick.findIndex((c) => c.id === winningCard.id);
    const trickWinner = (state.leadPlayer + winningCardIndex) % 4;
    const winnerTeam = state.players[trickWinner].team;

    // Update teams' tricks won - don't add cards here, let updateRunningScore handle it
    const newTeams = [...state.teams] as [Team, Team];

    // Update running score after each trick - this is the key enhancement
    const stateWithUpdatedScore = updateRunningScore(
      {
        ...state,
        players: newPlayers,
        teams: newTeams,
        currentTrick: newTrick, // Pass the complete trick with all 4 cards
      },
      trickWinner
    );

    // Get current running scores
    const team0CurrentPoints =
      stateWithUpdatedScore.teams[0].currentRoundPoints;
    const team1CurrentPoints =
      stateWithUpdatedScore.teams[1].currentRoundPoints;

    // Check if this was the last trick
    if (state.trickNumber === 10) {
      // Update game scores - mantieni precisione decimale per punteggio corretto
      const newGameScore: [number, number] = [
        state.gameScore[0] + team0CurrentPoints,
        state.gameScore[1] + team1CurrentPoints,
      ];

      // Check if the game is over (team reached victory points)
      // Game is over only if one team has reached victory points AND has more points than the other
      // If both teams reach victory points but are tied, the game continues until there's a clear winner
      const gameOver =
        (newGameScore[0] >= state.victoryPoints ||
          newGameScore[1] >= state.victoryPoints) &&
        newGameScore[0] !== newGameScore[1];

      return {
        ...stateWithUpdatedScore,
        currentTrick: [],
        leadSuit: null,
        currentPlayer: trickWinner,
        leadPlayer: trickWinner, // Update leadPlayer for next trick
        trickNumber: 1, // Reset for next round
        gamePhase: gameOver ? "gameOver" : "roundOver", // Changed from "scoring" to "roundOver" for clarity
        lastTrickWinner: trickWinner,
        roundScore: [team0CurrentPoints, team1CurrentPoints],
        gameScore: newGameScore,
        message: gameOver
          ? `Gioco finito! ${
              newGameScore[0] > newGameScore[1] ? "Squadra 1" : "Squadra 2"
            } ha vinto!`
          : newGameScore[0] >= state.victoryPoints &&
            newGameScore[1] >= state.victoryPoints &&
            newGameScore[0] === newGameScore[1]
          ? `Entrambe le squadre hanno raggiunto ${state.victoryPoints} punti ma sono in pareggio (${newGameScore[0]}-${newGameScore[1]}). La partita continua fino al primo vincitore!`
          : `${state.players[trickWinner].name} ha vinto l'ultima presa. Punteggio mano: ${team0CurrentPoints}-${team1CurrentPoints}`,
      };
    }

    // Not the last trick, continue with the next one
    return {
      ...stateWithUpdatedScore,
      currentTrick: [],
      leadSuit: null,
      currentPlayer: trickWinner,
      leadPlayer: trickWinner, // Update leadPlayer for next trick
      trickNumber: state.trickNumber + 1,
      lastTrickWinner: trickWinner,
      message: `${
        state.players[trickWinner].name
      } ha vinto la presa. Punteggio attuale: Squadra 1 (${team0CurrentPoints.toFixed(
        1
      )}) - Squadra 2 (${team1CurrentPoints.toFixed(1)})`,
    };
  }

  // Trick is not complete yet, move to next player
  return {
    ...state,
    players: newPlayers,
    currentTrick: newTrick,
    currentPlayer: (state.currentPlayer + 1) % 4,
    message: `${currentPlayer.name} ha giocato ${card.displayName}`,
  };
};

export const startNewRound = (state: GameState): GameState => {
  if (state.gamePhase !== "scoring" && state.gamePhase !== "roundOver") {
    return state;
  }

  // AI memory reset removed for simplification

  // Create and shuffle a new deck
  const deck = shuffleDeck(createDeck());
  // Reset player hands and deal new cards
  const newPlayers = [...state.players];
  let hands = dealCards(deck, 4, 10);

  // Applica debug per forzare la maraffa se abilitato
  hands = applyDebugMaraffaForcing(hands);

  newPlayers.forEach((player, index) => {
    player.hand = hands[index];
  });

  // Reset teams' tricks won and current round points
  const newTeams: [Team, Team] = [
    {
      ...state.teams[0],
      tricksWon: [],
      currentRoundPoints: 0,
      currentRoundFigures: 0,
    },
    {
      ...state.teams[1],
      tricksWon: [],
      currentRoundPoints: 0,
      currentRoundFigures: 0,
    },
  ];

  // Find player with four of coins
  const playerWithFourOfCoins = newPlayers.findIndex((player) =>
    player.hand.some(
      (card) => card.suit === Suit.Coins && card.rank === Rank.Four
    )
  );
  // Determina chi sceglie la briscola per questo round
  let newCurrentPlayer = 0;

  if (state.lastTrumpSelector === null) {
    // Primo round del gioco: il 4 di denari sceglie la briscola
    newCurrentPlayer = playerWithFourOfCoins !== -1 ? playerWithFourOfCoins : 0;
  } else {
    // Round successivi: la scelta passa in senso ORARIO (aggiunge 1)
    // rispetto a chi ha scelto la briscola nel round precedente
    newCurrentPlayer = (state.lastTrumpSelector + 1) % 4;
  }

  // 🎯 NUOVO: Verifica se il giocatore corrente ha una Maraffa automatica
  const tempState = {
    ...state,
    players: newPlayers,
    currentPlayer: newCurrentPlayer,
  };
  const automaticMaraffa = checkForAutomaticMaraffa(tempState);

  // 🎯 CORREZIONE: Se c'è una maraffa automatica, applica immediatamente la selezione della briscola
  // MA solo se è la CPU (non il giocatore umano con id 0)
  if (
    automaticMaraffa.hasMaraffa &&
    automaticMaraffa.maraffaSuit &&
    newCurrentPlayer !== 0
  ) {
    const newStateWithTrump = {
      ...state,
      deck,
      players: newPlayers,
      teams: newTeams,
      currentPlayer: newCurrentPlayer,
      leadPlayer: newCurrentPlayer,
      trumpSuit: null,
      leadSuit: null,
      currentTrick: [],
      trickNumber: 1,
      gamePhase: "selectTrump" as const,
      playerWithFourOfCoins,
      roundScore: [0, 0] as [number, number],
      lastTrickWinner: null,
      announcedAction: null,
      currentRoundScoreHistory: { team0: [], team1: [] },
      automaticMaraffa: automaticMaraffa,
    };

    // Applica automaticamente la selezione della briscola solo per la CPU
    return selectTrump(newStateWithTrump, automaticMaraffa.maraffaSuit);
  }

  return {
    ...state,
    deck,
    players: newPlayers,
    teams: newTeams,
    currentPlayer: newCurrentPlayer,
    leadPlayer: newCurrentPlayer, // Set leadPlayer for the new round
    trumpSuit: null,
    leadSuit: null,
    currentTrick: [],
    trickNumber: 1,
    gamePhase: "selectTrump",
    playerWithFourOfCoins,
    roundScore: [0, 0],
    lastTrickWinner: null,
    announcedAction: null,
    currentRoundScoreHistory: { team0: [], team1: [] },
    automaticMaraffa: automaticMaraffa.hasMaraffa ? automaticMaraffa : null, // Mantieni info per UI
    message:
      automaticMaraffa.hasMaraffa && newCurrentPlayer === 0
        ? `${newPlayers[newCurrentPlayer].name} ha la Maraffa! Puoi scegliere il seme per la briscola.`
        : state.lastTrumpSelector === null
        ? `${newPlayers[newCurrentPlayer].name} ha il 4 di Denari e deve scegliere la briscola`
        : `${newPlayers[newCurrentPlayer].name} deve scegliere la briscola`,
  };
};

export const declareGameWin = (state: GameState, teamId: 0 | 1): GameState => {
  if (state.gamePhase !== "play") return state;

  const team = state.teams[teamId];
  const currentScore = state.gameScore[teamId];

  // Calcola il punteggio utilizzando il nuovo sistema
  const expectedScore = calculateScore(team.tricksWon);
  const totalExpectedPoints =
    expectedScore.acePoints + Math.floor(expectedScore.figureCount / 3);

  if (currentScore + totalExpectedPoints >= state.victoryPoints) {
    console.log("🎯 gameLogic: Partita finita - vittoria squadra", teamId + 1);
    return {
      ...state,
      gamePhase: "gameOver",
      gameScore: [
        teamId === 0 ? currentScore + totalExpectedPoints : state.gameScore[0],
        teamId === 1 ? currentScore + totalExpectedPoints : state.gameScore[1],
      ],
      message: `Squadra ${
        teamId + 1
      } ha dichiarato vittoria e ha raggiunto i punti necessari.`,
    };
  } else {
    console.log(
      "🎯 gameLogic: Partita finita - dichiarazione fallita squadra",
      teamId + 1
    );
    return {
      ...state,
      gamePhase: "gameOver",
      roundScore: teamId === 0 ? [0, 11] : [11, 0],
      gameScore: [
        state.gameScore[0] + (teamId === 0 ? 0 : 11),
        state.gameScore[1] + (teamId === 1 ? 0 : 11),
      ],
      message: `Squadra ${
        teamId + 1
      } ha dichiarato vittoria ma non ha abbastanza punti. Squadra ${
        teamId === 0 ? 2 : 1
      } vince 11-0.`,
    };
  }
};

export const startNewGame = (victoryPoints: number = 31): GameState => {
  return initializeGameState(victoryPoints);
};

// Add a new function to handle game state updates from online
export const updateGameState = (
  state: GameState,
  newState: GameState
): GameState => {
  return {
    ...newState,
  };
};

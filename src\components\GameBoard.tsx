import React from "react";
import PlayedCards from "@/components/PlayedCards";
import TrumpAnnouncement from "@/components/TrumpAnnouncement";
import ActionAnnouncement from "@/components/ActionAnnouncement";
import { Card as CardType } from "@/utils/game/cardUtils";
import { GameState } from "@/utils/game/gameLogic";
import {
  ActionAnnouncementType,
  TrumpAnnouncementType,
} from "@/types/gameTypes";
import { useTableMat } from "@/hooks/useTableMat";

type GameBoardProps = {
  gameState: GameState;
  displayCards: CardType[];
  lastPlayedCard: CardType | null;
  getPlayedCardPosition: (trickIndex: number) => {
    className: string;
    zIndex: number;
  };
  getPlayerTeam: (index: number) => number | undefined;
  trumpAnnouncement: TrumpAnnouncementType;
  actionAnnouncement: ActionAnnouncementType;
  getTrumpSuitImage: () => string;
  getTrumpSuitName: () => string;
  isMobile: boolean;
  isDesktopLayout: boolean;
  showingLastTrick?: boolean;
};

const GameBoard = ({
  gameState,
  displayCards,
  lastPlayedCard,
  getPlayedCardPosition,
  getPlayerTeam,
  trumpAnnouncement,
  actionAnnouncement,
  getTrumpSuitImage,
  getTrumpSuitName,
  isMobile,
  isDesktopLayout,
  showingLastTrick = false,
}: GameBoardProps) => {
  // Hook ottimizzato per gestire i tappetini
  const { image: tableMatImage, borderColor } = useTableMat();
  return (
    <div
      className={`flex-1 flex flex-col items-center justify-center relative ${
        isMobile ? "pb-[12rem]" : ""
      }`}
    >
      {/* Game action announcement */}
      <ActionAnnouncement
        actionAnnouncement={actionAnnouncement}
        playerName={
          actionAnnouncement.playerIndex >= 0
            ? gameState.players[actionAnnouncement.playerIndex]?.name || ""
            : ""
        }
      />
      {/* Trump selection announcement */}
      <TrumpAnnouncement trumpAnnouncement={trumpAnnouncement} />{" "}
      {/* Tavolo da gioco rustico romagnolo */}
      <div
        className={`w-full max-w-xs sm:max-w-sm aspect-square relative rounded-full shadow-xl overflow-visible ${
          isDesktopLayout ? "max-w-3xl mb-20" : ""
        }`}
        style={{
          background: tableMatImage
            ? `url(${tableMatImage})`
            : `radial-gradient(circle, #7d5a44 20%, #603f28 100%)`,
          backgroundSize: tableMatImage ? "cover" : "auto",
          backgroundPosition: tableMatImage ? "center" : "auto",
          backgroundRepeat: tableMatImage ? "no-repeat" : "auto",
          boxShadow: `0 8px 16px rgba(0, 0, 0, 0.2), 
                       inset 0 2px 40px rgba(255, 255, 255, 0.15), 
                       inset 0 -5px 30px rgba(0, 0, 0, 0.3)`,
          border: `12px solid`,
          borderColor: borderColor,
          borderRadius: `50%`,
          position: `relative`,
        }}
      >
        {" "}
        {/* Venature del legno - solo decorazione per tappetini senza immagine */}
        {!tableMatImage && (
          <div
            className="absolute inset-0 rounded-full opacity-20 animate-pattern-shift"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z' fill='%23f5ecd7' fill-opacity='0.2' fill-rule='evenodd'/%3E%3C/svg%3E")`,
            }}
          ></div>
        )}
        {/* Scritta circolare BRISCOLA */}
        {/* Nessuna scritta circolare esterna */}
        {/* Seme di briscola */}
        {gameState.trumpSuit && (
          <div
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-romagna-cream/80 backdrop-blur-sm rounded-full p-3 shadow-lg border-2 border-romagna-gold/30 z-20 flex items-center justify-center"
            style={{ position: "relative", width: 56, height: 56 }}
          >
            {/* Scritta circolare SVG dentro il cerchio piccolo */}
            <svg
              width="56"
              height="56"
              viewBox="0 0 56 56"
              className="absolute left-0 top-0 pointer-events-none select-none"
              style={{ zIndex: 1 }}
            >
              <defs>
                <path
                  id="circlePathBriscolaInnerSmall"
                  d="M27,27 m0,-21 a21,21 0 1,1 0,42 a21,21 0 1,1 0,-42"
                />
              </defs>
              <text
                fill="#b8860b"
                fontSize="9"
                fontFamily="'DynaPuff', cursive"
                fontWeight="bold"
                letterSpacing="1.5"
              >
                <textPath
                  xlinkHref="#circlePathBriscolaInnerSmall"
                  textAnchor="middle"
                  dominantBaseline="middle"
                >
                  BRISCOLA • BRISCOLA • BRISCOLA • BRISCOLA •
                </textPath>
              </text>
            </svg>
            <img
              src={getTrumpSuitImage()}
              alt={getTrumpSuitName()}
              className={`object-contain transform -rotate-12 relative z-10 ${
                getTrumpSuitName() === "Coppe" ? "w-10 h-10" : "w-12 h-12"
              }`}
            />
          </div>
        )}
        {/* Played cards */}
        <PlayedCards
          displayCards={displayCards}
          gameState={gameState}
          lastPlayedCard={lastPlayedCard}
          getPlayedCardPosition={getPlayedCardPosition}
          getPlayerTeam={getPlayerTeam}
          isMobile={isMobile}
          showingLastTrick={showingLastTrick}
        />
      </div>
    </div>
  );
};

export default GameBoard;

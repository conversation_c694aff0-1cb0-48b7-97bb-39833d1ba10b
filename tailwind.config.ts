import type { Config } from "tailwindcss";
import tailwindcssAnimate from "tailwindcss-animate";

export default {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
        maraffa: {
          background: "#f7f3e9", // Carta consumata/pergamena
          primary: "#8b2c1c", // Rosso mattone caldo
          secondary: "#af8d55", // Ocra/Oro antico
          accent: "#3a6b35", // Verde oliva
          card: "#faf6ed", // Carta chiara
          cardBorder: "#e6d9c4", // Bordo carta vintage
          text: "#392417", // Marrone scuro per testo
          dark: "#2a1a12", // Marrone quasi nero
          light: "#fdfbf6", // Crema chiaro quasi bianco
        },
        romagna: {
          blue: "#2b5871", // Blu polvere
          rust: "#c75d3a", // Terracotta/ruggine
          cream: "#f5ecd7", // Crema/avorio
          gold: "#d4a35a", // Oro antico
          wood: "#7d5a44", // Legno
          darkWood: "#5c4129", // Legno scuro
          lightWood: "#9a7b62", // Legno chiaro
          terracotta: "#e07a5f", // Terracotta più vivace
        },
        // 🎮 NUOVI COLORI PER TEMA GAMING VERDE
        gaming: {
          darkGreen: "#1a4d3a", // Verde scuro principale
          mediumGreen: "#0f3d2c", // Verde medio
          deepGreen: "#0a2e21", // Verde profondo
          shadowGreen: "#052016", // Verde ombra
          blackGreen: "#03140e", // Verde quasi nero
        },
        // 🎯 NUOVI COLORI TEAM - Identità visiva ben distinta
        team: {
          0: {
            primary: "#b8860b", // Giallo scuro (DarkGoldenrod)
            secondary: "#daa520", // Oro più brillante per accenti
            light: "#f5deb3", // Wheat per sfondi chiari
            dark: "#8b7d00", // Oro molto scuro per testi
          },
          1: {
            primary: "#8b0000", // Rosso scuro (DarkRed)
            secondary: "#dc143c", // Rosso cremisi per accenti
            light: "#ffe4e1", // MistyRose per sfondi chiari
            dark: "#654321", // Marrone scuro per testi
          },
        },
        playable: {
          border: "#8b2c1c", // Colore rosso mattone per carte giocabili
          glow: "rgba(139, 44, 28, 0.5)", // Bagliore rosso mattone
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      spacing: {
        "18": "4.5rem", // 72px - per h-18 nelle carte xs
        "30": "7.5rem", // 120px - per h-30 nelle carte md
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
        "fade-in": {
          "0%": { opacity: "0", transform: "translateY(10px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
        "slide-up": {
          "0%": { opacity: "0", transform: "translateY(20px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
        "slide-right": {
          "0%": { opacity: "0", transform: "translateX(-20px)" },
          "100%": { opacity: "1", transform: "translateX(0)" },
        },
        scale: {
          "0%": { transform: "scale(0.95)" },
          "100%": { transform: "scale(1)" },
        },
        float: {
          "0%, 100%": { transform: "translateY(0)" },
          "50%": { transform: "translateY(-5px)" },
        },
        "card-flip": {
          "0%": { transform: "rotateY(0deg)" },
          "100%": { transform: "rotateY(180deg)" },
        },
        "pulse-border": {
          "0%, 100%": { boxShadow: "0 0 0 0 rgba(59, 130, 246, 0)" },
          "50%": { boxShadow: "0 0 0 4px rgba(59, 130, 246, 0.4)" },
        },
        glow: {
          "0%, 100%": { boxShadow: "0 0 5px 2px rgba(59, 130, 246, 0.3)" },
          "50%": { boxShadow: "0 0 10px 5px rgba(59, 130, 246, 0.5)" },
        },
        "card-play": {
          "0%": { transform: "scale(1.2) translateY(-20px)", opacity: "0" },
          "100%": { transform: "scale(1) translateY(0)", opacity: "1" },
        },
        "winner-indicator": {
          "0%": { transform: "scale(0)", opacity: "0" },
          "50%": { transform: "scale(1.2)", opacity: "0.7" },
          "100%": { transform: "scale(1)", opacity: "1" },
        },
        "card-hover": {
          "0%": { transform: "translateY(0)" },
          "100%": { transform: "translateY(-5px)" },
        },
        "card-glow": {
          "0%, 100%": { boxShadow: "0 0 4px 2px rgba(59, 130, 246, 0.3)" },
          "50%": { boxShadow: "0 0 8px 4px rgba(59, 130, 246, 0.5)" },
        },
        "subtle-bounce": {
          "0%, 100%": { transform: "translateY(0)" },
          "50%": { transform: "translateY(-3px)" },
        },
        "book-flip": {
          "0%": { transform: "rotateY(0deg) scale(1)" },
          "50%": { transform: "rotateY(90deg) scale(1.1)" },
          "100%": { transform: "rotateY(0deg) scale(1)" },
        },
        "book-float": {
          "0%, 100%": { transform: "translateY(0px)" },
          "50%": { transform: "translateY(-4px)" },
        },
        "book-glow": {
          "0%, 100%": { textShadow: "0 0 5px rgba(255, 215, 0, 0.5)" },
          "50%": { textShadow: "0 0 15px rgba(255, 215, 0, 0.8)" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "fade-in": "fade-in 0.5s ease-out forwards",
        "slide-up": "slide-up 0.6s ease-out forwards",
        "slide-right": "slide-right 0.6s ease-out forwards",
        scale: "scale 0.3s ease-out forwards",
        float: "float 3s ease-in-out infinite",
        "card-flip": "card-flip 0.5s ease-out forwards",
        "pulse-border": "pulse-border 2s infinite",
        glow: "glow 2s infinite",
        "card-play": "card-play 0.3s ease-out forwards",
        "winner-indicator": "winner-indicator 0.5s ease-out forwards",
        "card-hover": "card-hover 0.3s ease-out",
        "card-glow": "card-glow 1.5s infinite",
        "subtle-bounce": "subtle-bounce 2s ease-in-out infinite",
        "rustic-shimmer": "shimmer 3s ease-in-out infinite",
        "tavern-flicker": "flicker 4s ease-in-out infinite",
        "wood-grain": "grain 10s linear infinite",
        "book-flip": "book-flip 1s ease-in-out",
        "book-float": "book-float 2s ease-in-out infinite",
        "book-glow": "book-glow 2s ease-in-out infinite",
        "winning-card-spectacular": "winningCardSpectacular 1s ease-in-out",
      },
      transitionDuration: {
        "2000": "2000ms",
      },
      transitionDelay: {
        "0": "0ms",
        "100": "100ms",
        "200": "200ms",
        "300": "300ms",
        "400": "400ms",
        "500": "500ms",
      },
    },
  },
  plugins: [tailwindcssAnimate],
} satisfies Config;
